#!/bin/bash
# Aria2完整进程守护和开机自启配置脚本
# 包含：aria2主服务、监控告警、自适应配置、清理定时器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 创建aria2主服务
create_aria2_service() {
    log_step "创建aria2主服务..."
    
    cat > /etc/systemd/system/aria2.service << 'EOF'
[Unit]
Description=Aria2 Download Manager
Documentation=man:aria2c(1)
After=network.target
Wants=network.target

[Service]
Type=forking
User=www
Group=www
WorkingDirectory=/www/wwwroot/JAVAPI.COM
ExecStartPre=/bin/mkdir -p /downloads /config /var/log/aria2
ExecStartPre=/bin/chown -R www:www /downloads /config
ExecStart=/usr/bin/aria2c --conf-path=/config/aria2.conf --daemon=true
ExecReload=/bin/kill -HUP $MAINPID
ExecStop=/bin/kill -TERM $MAINPID
PIDFile=/var/run/aria2.pid
Restart=always
RestartSec=10
TimeoutStartSec=30
TimeoutStopSec=30

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/downloads /config /var/log/aria2 /var/run

[Install]
WantedBy=multi-user.target
EOF

    log_info "aria2主服务创建完成"
}

# 更新监控告警服务（添加依赖）
update_monitor_service() {
    log_step "更新监控告警服务..."
    
    cat > /etc/systemd/system/aria2-monitor-alert.service << 'EOF'
[Unit]
Description=Aria2 Monitor Alert System
Documentation=https://github.com/aria2/aria2
After=network.target aria2.service
Wants=aria2.service
Requires=aria2.service

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/www/wwwroot/JAVAPI.COM
ExecStartPre=/bin/sleep 10
ExecStart=/usr/bin/python3 scripts/aria2_monitor_alert.py monitor 60
Restart=always
RestartSec=15
TimeoutStartSec=60
TimeoutStopSec=30
StandardOutput=journal
StandardError=journal

# 健康检查
ExecStartPost=/bin/sleep 5
ExecStartPost=/bin/bash -c 'if ! pgrep -f "aria2_monitor_alert.py"; then exit 1; fi'

# 资源限制
MemoryMax=256M
CPUQuota=50%

[Install]
WantedBy=multi-user.target
EOF

    log_info "监控告警服务更新完成"
}

# 更新自适应配置服务（添加依赖）
update_adaptive_service() {
    log_step "更新自适应配置服务..."
    
    cat > /etc/systemd/system/aria2-adaptive-config.service << 'EOF'
[Unit]
Description=Aria2 Adaptive Configuration Optimizer
Documentation=https://github.com/aria2/aria2
After=network.target aria2.service aria2-monitor-alert.service
Wants=aria2.service aria2-monitor-alert.service
Requires=aria2.service

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/www/wwwroot/JAVAPI.COM
ExecStartPre=/bin/sleep 20
ExecStart=/usr/bin/python3 scripts/aria2_adaptive_config.py monitor 6
Restart=always
RestartSec=30
TimeoutStartSec=120
TimeoutStopSec=30
StandardOutput=journal
StandardError=journal

# 健康检查
ExecStartPost=/bin/sleep 5
ExecStartPost=/bin/bash -c 'if ! pgrep -f "aria2_adaptive_config.py"; then exit 1; fi'

# 资源限制
MemoryMax=256M
CPUQuota=30%

[Install]
WantedBy=multi-user.target
EOF

    log_info "自适应配置服务更新完成"
}

# 创建系统监控服务
create_system_monitor() {
    log_step "创建系统监控服务..."
    
    cat > /etc/systemd/system/aria2-system-monitor.service << 'EOF'
[Unit]
Description=Aria2 System Health Monitor
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/www/wwwroot/JAVAPI.COM
ExecStart=/bin/bash -c 'while true; do /usr/local/bin/aria2-diagnose --auto-fix; sleep 300; done'
Restart=always
RestartSec=60
TimeoutStartSec=30
TimeoutStopSec=10
StandardOutput=journal
StandardError=journal

# 资源限制
MemoryMax=128M
CPUQuota=20%

[Install]
WantedBy=multi-user.target
EOF

    log_info "系统监控服务创建完成"
}

# 创建故障恢复脚本
create_recovery_script() {
    log_step "创建故障恢复脚本..."
    
    cat > /usr/local/bin/aria2-recovery << 'EOF'
#!/bin/bash
# Aria2故障自动恢复脚本

LOG_FILE="/var/log/aria2_recovery.log"
TELEGRAM_BOT_TOKEN="**********************************************"
TELEGRAM_CHAT_ID="**********"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

send_telegram() {
    local message="$1"
    curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
        -d chat_id="${TELEGRAM_CHAT_ID}" \
        -d text="🚨 Aria2故障恢复: $message" \
        -d parse_mode="HTML" > /dev/null 2>&1
}

check_and_restart_service() {
    local service_name="$1"
    
    if ! systemctl is-active --quiet "$service_name"; then
        log_message "检测到 $service_name 服务异常，尝试重启"
        send_telegram "$service_name 服务异常，正在重启"
        
        systemctl restart "$service_name"
        sleep 10
        
        if systemctl is-active --quiet "$service_name"; then
            log_message "$service_name 服务重启成功"
            send_telegram "$service_name 服务重启成功"
        else
            log_message "$service_name 服务重启失败"
            send_telegram "$service_name 服务重启失败，需要人工干预"
        fi
    fi
}

# 检查核心服务
services=("aria2" "aria2-monitor-alert" "aria2-adaptive-config")

for service in "${services[@]}"; do
    check_and_restart_service "$service"
done

# 检查磁盘空间
disk_usage=$(df /downloads | awk 'NR==2 {print $5}' | sed 's/%//')
if [ "$disk_usage" -gt 90 ]; then
    log_message "磁盘空间不足: ${disk_usage}%"
    send_telegram "磁盘空间不足: ${disk_usage}%，正在清理"
    /usr/local/bin/aria2-emergency
fi

# 检查内存使用
memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [ "$memory_usage" -gt 85 ]; then
    log_message "内存使用过高: ${memory_usage}%"
    send_telegram "内存使用过高: ${memory_usage}%"
fi

log_message "故障恢复检查完成"
EOF

    chmod +x /usr/local/bin/aria2-recovery
    log_info "故障恢复脚本创建完成"
}

# 创建故障恢复定时器
create_recovery_timer() {
    log_step "创建故障恢复定时器..."
    
    cat > /etc/systemd/system/aria2-recovery.service << 'EOF'
[Unit]
Description=Aria2 Recovery Check
After=network.target

[Service]
Type=oneshot
User=root
ExecStart=/usr/local/bin/aria2-recovery
StandardOutput=journal
StandardError=journal
EOF

    cat > /etc/systemd/system/aria2-recovery.timer << 'EOF'
[Unit]
Description=Run Aria2 Recovery Check every 5 minutes
Requires=aria2-recovery.service

[Timer]
OnCalendar=*:0/5
Persistent=true

[Install]
WantedBy=timers.target
EOF

    log_info "故障恢复定时器创建完成"
}

# 停止现有的s6服务（如果存在）
stop_s6_services() {
    log_step "检查并停止s6服务..."
    
    if pgrep -f "s6-supervise aria2" > /dev/null; then
        log_warn "检测到s6-supervise运行的aria2，正在停止..."
        pkill -f "s6-supervise aria2" || true
        pkill -f "aria2c" || true
        sleep 5
        log_info "s6服务已停止"
    fi
}

# 启用所有服务
enable_all_services() {
    log_step "启用所有服务..."
    
    # 重新加载systemd配置
    systemctl daemon-reload
    
    # 启用并启动服务
    services=(
        "aria2.service"
        "aria2-monitor-alert.service" 
        "aria2-adaptive-config.service"
        "aria2-system-monitor.service"
        "aria2-cleaner-optimized.timer"
        "aria2-recovery.timer"
    )
    
    for service in "${services[@]}"; do
        log_info "启用 $service"
        systemctl enable "$service"
        
        if [[ "$service" == *.timer ]]; then
            systemctl start "$service"
        else
            systemctl restart "$service"
        fi
        
        sleep 2
    done
    
    log_info "所有服务启用完成"
}

# 验证服务状态
verify_services() {
    log_step "验证服务状态..."
    
    services=(
        "aria2.service"
        "aria2-monitor-alert.service" 
        "aria2-adaptive-config.service"
        "aria2-system-monitor.service"
        "aria2-cleaner-optimized.timer"
        "aria2-recovery.timer"
    )
    
    echo
    echo "📊 服务状态报告:"
    echo "================================"
    
    for service in "${services[@]}"; do
        if systemctl is-active --quiet "$service"; then
            echo -e "✅ $service - ${GREEN}运行中${NC}"
        else
            echo -e "❌ $service - ${RED}异常${NC}"
        fi
        
        if systemctl is-enabled --quiet "$service"; then
            echo -e "   🔄 开机自启: ${GREEN}已启用${NC}"
        else
            echo -e "   🔄 开机自启: ${RED}未启用${NC}"
        fi
        echo
    done
}

# 显示管理说明
show_management_guide() {
    log_info "🎉 Aria2完整进程守护配置完成！"
    echo
    echo "📋 服务架构:"
    echo "  🎯 aria2.service                 - 核心下载服务"
    echo "  🔔 aria2-monitor-alert.service   - 监控告警系统"
    echo "  ⚙️ aria2-adaptive-config.service - 自适应配置优化"
    echo "  🛡️ aria2-system-monitor.service  - 系统健康监控"
    echo "  🧹 aria2-cleaner-optimized.timer - 定时清理"
    echo "  🚨 aria2-recovery.timer          - 故障自动恢复"
    echo
    echo "📋 管理命令:"
    echo "  systemctl status aria2                    # 查看aria2状态"
    echo "  systemctl restart aria2                   # 重启aria2"
    echo "  systemctl stop aria2                      # 停止aria2"
    echo "  systemctl start aria2                     # 启动aria2"
    echo
    echo "  aria2-manager status                       # 查看所有组件状态"
    echo "  aria2-manager restart-all                 # 重启所有组件"
    echo "  aria2-recovery                            # 手动执行故障恢复"
    echo
    echo "📋 日志查看:"
    echo "  journalctl -u aria2 -f                   # 查看aria2日志"
    echo "  journalctl -u aria2-monitor-alert -f     # 查看监控日志"
    echo "  journalctl -u aria2-adaptive-config -f   # 查看优化日志"
    echo "  tail -f /var/log/aria2_recovery.log      # 查看恢复日志"
    echo
    echo "🔧 故障恢复:"
    echo "  - 每5分钟自动检查服务状态"
    echo "  - 自动重启异常服务"
    echo "  - 磁盘空间监控和清理"
    echo "  - Telegram实时告警通知"
    echo
    echo "📁 重要文件:"
    echo "  配置文件: /config/aria2.conf"
    echo "  下载目录: /downloads"
    echo "  日志目录: /var/log/aria2_*.log"
    echo "  恢复日志: /var/log/aria2_recovery.log"
}

# 主函数
main() {
    echo "🚀 Aria2完整进程守护和开机自启配置"
    echo "========================================"
    
    check_permissions
    stop_s6_services
    create_aria2_service
    update_monitor_service
    update_adaptive_service
    create_system_monitor
    create_recovery_script
    create_recovery_timer
    enable_all_services
    verify_services
    show_management_guide
    
    log_info "🎉 配置完成！系统已具备完整的进程守护和故障恢复能力"
}

# 执行主函数
main "$@"