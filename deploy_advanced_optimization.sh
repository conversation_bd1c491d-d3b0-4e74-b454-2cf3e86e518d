#!/bin/bash
# Aria2高级优化组件部署脚本
# 部署监控告警、自适应配置、统一管理工具

set -e

echo "🚀 开始部署Aria2高级优化组件..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 安装Python依赖
install_dependencies() {
    log_step "安装Python依赖包..."
    
    # 检查并安装psutil
    if ! python3 -c "import psutil" 2>/dev/null; then
        log_info "安装psutil..."
        pip3 install psutil
    fi
    
    # 检查并安装requests
    if ! python3 -c "import requests" 2>/dev/null; then
        log_info "安装requests..."
        pip3 install requests
    fi
    
    log_info "Python依赖安装完成"
}

# 设置脚本权限
setup_permissions() {
    log_step "设置脚本执行权限..."
    
    chmod +x scripts/aria2_monitor_alert.py
    chmod +x scripts/aria2_adaptive_config.py
    chmod +x scripts/aria2_manager.py
    
    log_info "脚本权限设置完成"
}

# 创建监控告警服务
create_monitor_service() {
    log_step "创建监控告警服务..."
    
    cat > /etc/systemd/system/aria2-monitor-alert.service << EOF
[Unit]
Description=Aria2 Monitor Alert System
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/www/wwwroot/JAVAPI.COM
ExecStart=/usr/bin/python3 scripts/aria2_monitor_alert.py monitor 60
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    systemctl daemon-reload
    systemctl enable aria2-monitor-alert
    
    log_info "监控告警服务创建完成"
}

# 创建自适应配置服务
create_adaptive_service() {
    log_step "创建自适应配置服务..."
    
    cat > /etc/systemd/system/aria2-adaptive-config.service << EOF
[Unit]
Description=Aria2 Adaptive Configuration Optimizer
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/www/wwwroot/JAVAPI.COM
ExecStart=/usr/bin/python3 scripts/aria2_adaptive_config.py monitor 6
Restart=always
RestartSec=30
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    systemctl daemon-reload
    systemctl enable aria2-adaptive-config
    
    log_info "自适应配置服务创建完成"
}

# 创建清理脚本定时器
create_cleaner_timer() {
    log_step "创建清理脚本定时器..."
    
    cat > /etc/systemd/system/aria2-cleaner-optimized.service << EOF
[Unit]
Description=Aria2 Cleaner Optimized
After=network.target

[Service]
Type=oneshot
User=root
WorkingDirectory=/www/wwwroot/JAVAPI.COM
ExecStart=/usr/bin/python3 scripts/aria2_cleaner_optimized.py all
StandardOutput=journal
StandardError=journal
EOF
    
    cat > /etc/systemd/system/aria2-cleaner-optimized.timer << EOF
[Unit]
Description=Run Aria2 Cleaner Optimized every 2 hours
Requires=aria2-cleaner-optimized.service

[Timer]
OnCalendar=0/2:00:00
Persistent=true

[Install]
WantedBy=timers.target
EOF
    
    systemctl daemon-reload
    systemctl enable aria2-cleaner-optimized.timer
    
    log_info "清理脚本定时器创建完成"
}

# 创建统一管理命令
create_management_commands() {
    log_step "创建统一管理命令..."
    
    # 创建aria2-manager命令
    cat > /usr/local/bin/aria2-manager << 'EOF'
#!/bin/bash
cd /www/wwwroot/JAVAPI.COM
python3 scripts/aria2_manager.py "$@"
EOF
    
    chmod +x /usr/local/bin/aria2-manager
    
    # 更新aria2-status命令
    cat > /usr/local/bin/aria2-status << 'EOF'
#!/bin/bash
cd /www/wwwroot/JAVAPI.COM
python3 scripts/aria2_manager.py status
EOF
    
    chmod +x /usr/local/bin/aria2-status
    
    # 更新aria2-emergency命令
    cat > /usr/local/bin/aria2-emergency << 'EOF'
#!/bin/bash
cd /www/wwwroot/JAVAPI.COM
python3 scripts/aria2_manager.py emergency
EOF
    
    chmod +x /usr/local/bin/aria2-emergency
    
    # 创建新的管理命令
    cat > /usr/local/bin/aria2-report << 'EOF'
#!/bin/bash
cd /www/wwwroot/JAVAPI.COM
python3 scripts/aria2_manager.py report
EOF
    
    chmod +x /usr/local/bin/aria2-report
    
    cat > /usr/local/bin/aria2-diagnose << 'EOF'
#!/bin/bash
cd /www/wwwroot/JAVAPI.COM
python3 scripts/aria2_manager.py diagnose
EOF
    
    chmod +x /usr/local/bin/aria2-diagnose
    
    log_info "统一管理命令创建完成"
}

# 启动服务
start_services() {
    log_step "启动高级优化服务..."
    
    # 启动监控告警
    systemctl start aria2-monitor-alert
    log_info "✅ 监控告警系统已启动"
    
    # 启动自适应配置（延迟启动，避免与监控冲突）
    sleep 5
    systemctl start aria2-adaptive-config
    log_info "✅ 自适应配置系统已启动"
    
    # 启动清理定时器
    systemctl start aria2-cleaner-optimized.timer
    log_info "✅ 清理定时器已启动"
    
    log_info "所有高级优化服务启动完成"
}

# 验证部署
verify_deployment() {
    log_step "验证部署状态..."
    
    # 检查服务状态
    services=("aria2-monitor-alert" "aria2-adaptive-config" "aria2-cleaner-optimized.timer")
    
    for service in "${services[@]}"; do
        if systemctl is-active --quiet "$service"; then
            log_info "✅ $service 运行正常"
        else
            log_error "❌ $service 运行异常"
        fi
    done
    
    # 检查命令可用性
    commands=("aria2-manager" "aria2-status" "aria2-emergency" "aria2-report" "aria2-diagnose")
    
    for cmd in "${commands[@]}"; do
        if command -v "$cmd" &> /dev/null; then
            log_info "✅ $cmd 命令可用"
        else
            log_error "❌ $cmd 命令不可用"
        fi
    done
    
    # 测试Telegram通知
    log_info "测试Telegram通知..."
    python3 scripts/aria2_monitor_alert.py test
}

# 显示使用说明
show_usage() {
    log_info "🎉 Aria2高级优化组件部署完成！"
    echo
    echo "📋 新增功能:"
    echo "  🔔 实时监控告警系统 - 多维度监控 + Telegram通知"
    echo "  ⚙️ 自适应配置优化 - 根据性能数据动态调整参数"
    echo "  🎛️ 统一管理工具 - 一站式管理所有组件"
    echo
    echo "📋 管理命令:"
    echo "  aria2-manager status          # 查看系统状态总览"
    echo "  aria2-manager start-all       # 启动所有组件"
    echo "  aria2-manager stop-all        # 停止所有组件"
    echo "  aria2-manager restart-all     # 重启所有组件"
    echo "  aria2-report                  # 生成综合报告"
    echo "  aria2-diagnose                # 系统诊断"
    echo "  aria2-emergency               # 紧急清理"
    echo
    echo "📋 组件管理:"
    echo "  aria2-manager start monitor-alert     # 启动监控告警"
    echo "  aria2-manager start adaptive-config   # 启动自适应配置"
    echo "  aria2-manager logs monitor-alert      # 查看监控日志"
    echo "  aria2-manager logs adaptive-config    # 查看优化日志"
    echo
    echo "📁 重要文件位置:"
    echo "  监控数据库: /var/lib/aria2_monitor.db"
    echo "  优化数据库: /var/lib/aria2_adaptive.db"
    echo "  配置备份: /backup/aria2_configs/"
    echo "  日志文件: /var/log/aria2_*.log"
    echo
    echo "🔔 Telegram通知:"
    echo "  系统会自动发送告警到您的Telegram"
    echo "  用户ID: 5278020429"
    echo "  Bot Token: 7760064802:AAE1CP_I8082M1Xpp1thMdd1UpXxt7s4ylc"
    echo
    echo "📖 详细文档: /www/wwwroot/JAVAPI.COM/SYSTEM_INTEGRATION_GUIDE.md"
}

# 主函数
main() {
    echo "🚀 Aria2高级优化组件部署"
    echo "========================================"
    
    check_permissions
    install_dependencies
    setup_permissions
    create_monitor_service
    create_adaptive_service
    create_cleaner_timer
    create_management_commands
    start_services
    verify_deployment
    show_usage
    
    log_info "🎉 高级优化组件部署完成！"
}

# 执行主函数
main "$@"