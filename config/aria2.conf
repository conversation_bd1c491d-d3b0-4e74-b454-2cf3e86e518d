# 超级优化配置 - 适用于成千上万磁力链接
# 内存优化 + 高并发 + 稳定性优先
# 服务器配置: 8核16G 2.5G带宽

## 文件保存设置 ##
dir=/downloads
disk-cache=128M          # 降低到128M，为更多任务留出内存
file-allocation=falloc
no-file-allocation-limit=64M
continue=true
always-resume=false
max-resume-failure-tries=0
remote-time=true
allow-overwrite=true         # 允许覆盖已存在文件
auto-file-renaming=false     # 禁用自动重命名，直接覆盖

## 进度保存设置（内存优化）##
input-file=/config/aria2.session
save-session=/config/aria2.session
save-session-interval=30     # 增加到30秒，减少磁盘I/O
auto-save-interval=120       # 增加到2分钟，减少磁盘I/O
force-save=false

## 超高并发下载设置 ##
max-file-not-found=3         # 减少重试，快速释放资源
max-tries=2                  # 减少重试次数
retry-wait=3
connect-timeout=10           # 缩短连接超时
timeout=20                   # 缩短总超时

# 关键优化：超高并发数
max-concurrent-downloads=100  # 增加到100个并发任务
max-connection-per-server=8   # 降低单服务器连接，避免被封
split=16                      # 降低分片数，减少内存使用
min-split-size=4M            # 增加最小分片大小
piece-length=1M              # 适中的分片大小
allow-piece-length-change=true

# 速度和资源优化
lowest-speed-limit=1K
max-overall-download-limit=0
max-download-limit=0
disable-ipv6=true
http-accept-gzip=true
reuse-uri=true
no-netrc=true
allow-overwrite=false
auto-file-renaming=true
content-disposition-default-utf8=true

## BT/磁力链接超级优化 ##
listen-port=6888
dht-listen-port=6888
enable-dht=true
enable-dht6=false
dht-file-path=/config/dht.dat
dht-file-path6=/config/dht6.dat
dht-entry-point=dht.transmissionbt.com:6881
dht-entry-point6=dht.transmissionbt.com:6881
bt-enable-lpd=true
enable-peer-exchange=true

# BT连接数优化（内存友好）
bt-max-peers=100             # 降低到100，减少内存使用
bt-request-peer-speed-limit=20K  # 降低阈值
max-overall-upload-limit=5M  # 限制总上传，保证下载带宽
max-upload-limit=500K        # 单任务上传限制

# 快速做种策略（释放资源）
seed-ratio=0.05              # 极低分享率，快速释放
seed-time=5                  # 最多做种5分钟
bt-hash-check-seed=false
bt-seed-unverified=true
bt-prioritize-piece=head=8M,tail=8M
rpc-save-upload-metadata=false  # 关闭元数据保存，节省内存

# 快速完成策略
follow-torrent=true
pause-metadata=false
bt-save-metadata=false       # 关闭元数据保存
bt-load-saved-metadata=false # 关闭元数据加载
bt-remove-unselected-file=true
bt-force-encryption=false
bt-detach-seed-only=true

# 连接优化
bt-tracker-connect-timeout=3  # 缩短tracker连接超时
bt-tracker-timeout=8         # 缩短tracker超时

## 客户端伪装 ##
user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
peer-agent=qBittorrent/4.4.5
peer-id-prefix=-qB4450-

## RPC 设置 ##
enable-rpc=true
rpc-allow-origin-all=true
rpc-listen-all=true
rpc-listen-port=6800
rpc-secret=aria2secret
rpc-max-request-size=10M     # 降低RPC请求大小限制

## 高级选项（内存优化）##
async-dns=true
async-dns-server=8.8.8.8,1.1.1.1

## 日志设置（最小化）##
console-log-level=error      # 只显示错误，减少日志开销
quiet=true                   # 安静模式
summary-interval=600         # 10分钟输出一次摘要
show-console-readout=false

## 内存和性能优化 ##
retry-on-400=false           # 关闭400错误重试
retry-on-403=false           # 关闭403错误重试
retry-on-406=false           # 关闭406错误重试
retry-on-unknown=false       # 关闭未知错误重试

# 内存管理优化
check-integrity=false        # 跳过完整性检查，节省时间和内存
realtime-chunk-checksum=false # 关闭实时校验，节省CPU和内存

## 精简的Tracker列表（高质量tracker）##
bt-tracker=udp://tracker.opentrackr.org:1337/announce,udp://open.stealth.si:80/announce,udp://exodus.desync.com:6969/announce,udp://tracker.torrent.eu.org:451/announce,udp://tracker.dler.org:6969/announce,udp://tracker.dler.com:6969/announce,udp://opentracker.io:6969/announce,udp://tracker.openbittorrent.com:6969/announce,udp://tracker.opentrackr.org:1337/announce,udp://tracker.coppersurfer.tk:6969/announce,udp://tracker.leechers-paradise.org:6969/announce,udp://zer0day.ch:1337/announce,udp://explodie.org:6969/announce