# 内存优化 + 高并发 + 稳定性优先
# 服务器配置: 8核16G 2.5G带宽

## 文件保存设置 ##
dir=/downloads
# 降低到128M，为更多任务留出内存
disk-cache=128M
file-allocation=falloc
no-file-allocation-limit=64M
continue=true
always-resume=false
max-resume-failure-tries=0
remote-time=true
# 允许覆盖已存在文件
allow-overwrite=true
# 禁用自动重命名，直接覆盖
auto-file-renaming=false

## 进度保存设置（内存优化）##
input-file=/config/aria2.session
save-session=/config/aria2.session
# 增加到30秒，减少磁盘I/O
save-session-interval=30
# 增加到2分钟，减少磁盘I/O
auto-save-interval=120

## 下载设置（内存优化）##
# 减少重试，快速释放资源
max-file-not-found=3
# 减少重试次数
max-tries=2
retry-wait=2
# 缩短连接超时
connect-timeout=10
# 缩短总超时
timeout=20

## 并发设置（高并发优化）##
# 增加到100个并发任务
max-concurrent-downloads=100
# 降低单服务器连接，避免被封
max-connection-per-server=8
# 降低分片数，减少内存使用
split=16
# 增加最小分片大小
min-split-size=4M
# 适中的分片大小
piece-length=1M

## HTTP/HTTPS 设置 ##
user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
http-accept-gzip=true
reuse-uri=false
no-netrc=true
content-disposition-default-utf8=true

## 代理设置 ##
# all-proxy=
# all-proxy-user=
# all-proxy-passwd=

## BitTorrent 设置（内存优化）##
enable-dht=true
enable-dht6=false
enable-peer-exchange=true
# 降低到100，减少内存使用
bt-max-peers=100
# 降低阈值
bt-request-peer-speed-limit=20K
# 限制总上传，保证下载带宽
max-overall-upload-limit=5M
# 单任务上传限制
max-upload-limit=500K

## 做种设置（快速释放）##
# 极低分享率，快速释放
seed-ratio=0.05
# 最多做种5分钟
seed-time=5

## RPC 设置 ##
enable-rpc=true
# 关闭元数据保存，节省内存
rpc-save-upload-metadata=false
rpc-listen-all=true
rpc-allow-origin-all=true

## BitTorrent 优化（内存节省）##
# 关闭元数据保存
bt-save-metadata=false
# 关闭元数据加载
bt-load-saved-metadata=false
bt-hash-check-seed=false
bt-seed-unverified=false
bt-stop-timeout=0
# 缩短tracker连接超时
bt-tracker-connect-timeout=3
# 缩短tracker超时
bt-tracker-timeout=8

## 高级设置 ##
disable-ipv6=false
max-overall-download-limit=0
max-download-limit=0
optimize-concurrent-downloads=true
auto-save-interval=60
force-save=true
log-level=warn
# 降低RPC请求大小限制
rpc-max-request-size=10M

## 日志设置（性能优化）##
log=/var/log/aria2/aria2.log
# 只显示错误，减少日志开销
console-log-level=error
# 安静模式
quiet=true
# 10分钟输出一次摘要
summary-interval=600

## 错误处理优化 ##
# 这些选项在当前版本不支持，已移除

# 内存管理优化
# 跳过完整性检查，节省时间和内存
check-integrity=false
# 关闭实时校验，节省CPU和内存
realtime-chunk-checksum=false

## 精简的Tracker列表（高质量tracker）##
bt-tracker=udp://tracker.opentrackr.org:1337/announce,udp://open.stealth.si:80/announce,udp://exodus.desync.com:6969/announce,udp://tracker.torrent.eu.org:451/announce,udp://tracker.dler.org:6969/announce,udp://tracker.dler.com:6969/announce,udp://opentracker.io:6969/announce,udp://tracker.openbittorrent.com:6969/announce,udp://tracker.opentrackr.org:1337/announce,udp://tracker.coppersurfer.tk:6969/announce,udp://tracker.leechers-paradise.org:6969/announce,udp://zer0day.ch:1337/announce,udp://explodie.org:6969/announce

## 进程管理 ##
# daemon=true (由systemd管理，不需要daemon模式)